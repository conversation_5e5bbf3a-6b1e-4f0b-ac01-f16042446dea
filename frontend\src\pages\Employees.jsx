import React, { useState, useMemo } from 'react';
import {
  UserGroupIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  UserIcon
} from '@heroicons/react/24/outline';
import { useEmployees } from '../hooks/useEmployees';
import { useCampaigns } from '../hooks/useCampaigns';
import Pagination from '../components/ui/Pagination';

const Employees = () => {
  // State for filters and pagination
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCampaign, setSelectedCampaign] = useState('');
  const [currentPage, setCurrentPage] = useState(1);

  const pageSize = 6; // Selon vos préférences mémoire

  // Fetch campaigns for filter dropdown
  const { data: campaignsResponse } = useCampaigns({ page_size: 1000 });
  const campaigns = campaignsResponse?.data || [];

  // Helper function to get campaign name
  const getCampaignName = (campaignId) => {
    const campaign = campaigns.find(c => c.id === campaignId);
    return campaign?.title || 'Unknown Campaign';
  };

  // Prepare query parameters for employees
  const queryParams = useMemo(() => ({
    page: 1,
    page_size: 1000, // Récupérer tous pour pagination côté client
    search: '', // On filtre côté client
    ...(selectedCampaign && { campaign: selectedCampaign })
  }), [selectedCampaign]);

  const { data: employeesResponse, isLoading, error } = useEmployees(queryParams);
  const employees = employeesResponse?.results || employeesResponse || [];

  // Group employees by email to avoid duplicates and filter
  const uniqueEmployees = useMemo(() => {
    if (!employees) return [];

    // Group employees by email
    const employeeMap = new Map();

    employees.forEach(employee => {
      const email = employee.email;
      if (employeeMap.has(email)) {
        // Add campaign to existing employee
        const existing = employeeMap.get(email);
        existing.campaigns.push({
          id: employee.campaign,
          name: getCampaignName(employee.campaign)
        });
        // Merge attributes if any
        if (employee.attributes_dict) {
          existing.attributes_dict = { ...existing.attributes_dict, ...employee.attributes_dict };
        }
      } else {
        // Create new unique employee entry
        employeeMap.set(email, {
          ...employee,
          campaigns: [{
            id: employee.campaign,
            name: getCampaignName(employee.campaign)
          }],
          attributes_dict: employee.attributes_dict || {}
        });
      }
    });

    return Array.from(employeeMap.values());
  }, [employees, campaigns]);

  // Client-side filtering
  const filteredEmployees = useMemo(() => {
    return uniqueEmployees.filter(employee => {
      const matchesSearch = !searchTerm ||
        employee.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        employee.email?.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesCampaign = !selectedCampaign ||
        employee.campaigns.some(campaign => campaign.id.toString() === selectedCampaign);

      return matchesSearch && matchesCampaign;
    });
  }, [uniqueEmployees, searchTerm, selectedCampaign]);

  // Pagination calculations
  const totalItems = filteredEmployees.length;
  const totalPages = Math.ceil(totalItems / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const paginatedEmployees = filteredEmployees.slice(startIndex, startIndex + pageSize);

  // Handlers
  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1); // Reset to first page
  };

  const handleCampaignFilter = (e) => {
    setSelectedCampaign(e.target.value);
    setCurrentPage(1);
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };



  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  if (error) {
    return (
      <div className="max-w-7xl mx-auto">
        <div className="bg-red-50 border border-red-200 rounded-xl p-6 text-center">
          <div className="text-red-600 mb-2">⚠️ Error loading employees</div>
          <div className="text-red-500 text-sm">{error.message || 'Something went wrong'}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto space-y-4">
      {/* Compact Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-warmGray-800">Employees</h1>
          <p className="text-sm text-warmGray-600 mt-1">
            {totalItems} employee{totalItems !== 1 ? 's' : ''} total
          </p>
        </div>
      </div>

      {/* Compact Search & Filter */}
      <div className="bg-white rounded-xl shadow-sm border border-warmGray-200 p-4">
        <div className="flex flex-col sm:flex-row gap-3">
          {/* Enhanced Search */}
          <div className="relative flex-1">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-4 w-4 text-warmGray-400" />
            </div>
            <input
              type="text"
              placeholder="Search by name or email..."
              value={searchTerm}
              onChange={handleSearchChange}
              className="block w-full pl-9 pr-3 py-2.5 border border-warmGray-300 rounded-lg text-sm placeholder-warmGray-500 focus:outline-none focus:ring-2 focus:ring-[#E8C4A0] focus:border-[#E8C4A0] transition-colors"
            />
            {searchTerm && (
              <button
                onClick={() => setSearchTerm('')}
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
              >
                <span className="text-warmGray-400 hover:text-warmGray-600 text-sm">✕</span>
              </button>
            )}
          </div>

          {/* Enhanced Campaign Filter */}
          <div className="relative sm:w-56">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FunnelIcon className="h-4 w-4 text-warmGray-400" />
            </div>
            <select
              value={selectedCampaign}
              onChange={handleCampaignFilter}
              className="block w-full pl-9 pr-8 py-2.5 border border-warmGray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[#E8C4A0] focus:border-[#E8C4A0] bg-white appearance-none transition-colors"
            >
              <option value="">All Campaigns</option>
              {campaigns.map(campaign => (
                <option key={campaign.id} value={campaign.id}>
                  {campaign.title}
                </option>
              ))}
            </select>
            <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
              <svg className="h-4 w-4 text-warmGray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>

          {/* Results Counter */}
          {(searchTerm || selectedCampaign) && (
            <div className="flex items-center px-3 py-2.5 bg-[#E8C4A0]/10 rounded-lg">
              <span className="text-sm text-[#8B6F47] font-medium">
                {filteredEmployees.length} result{filteredEmployees.length !== 1 ? 's' : ''}
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Compact Employees Table */}
      <div className="bg-white rounded-xl shadow-sm border border-warmGray-200 overflow-hidden">
        {isLoading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-2 border-[#E8C4A0] border-t-transparent mx-auto mb-3"></div>
            <div className="text-warmGray-600 text-sm">Loading employees...</div>
          </div>
        ) : paginatedEmployees.length === 0 ? (
          <div className="text-center py-12">
            <UserGroupIcon className="h-12 w-12 text-warmGray-400 mx-auto mb-3" />
            <h3 className="text-lg font-medium text-warmGray-800 mb-1">
              {searchTerm || selectedCampaign ? 'No employees found' : 'No employees yet'}
            </h3>
            <p className="text-warmGray-600 text-sm">
              {searchTerm || selectedCampaign
                ? 'Try adjusting your search or filter criteria'
                : 'Employees will appear here once they are imported into campaigns'
              }
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              {/* Compact Table Header */}
              <thead>
                <tr className="bg-warmGray-50/50 border-b border-warmGray-200">
                  <th className="text-left py-3 px-4 font-medium text-warmGray-600 text-xs uppercase tracking-wide">
                    Member
                  </th>
                  <th className="text-left py-3 px-4 font-medium text-warmGray-600 text-xs uppercase tracking-wide">
                    Email
                  </th>
                  <th className="text-left py-3 px-4 font-medium text-warmGray-600 text-xs uppercase tracking-wide">
                    Campaigns
                  </th>
                  <th className="text-left py-3 px-4 font-medium text-warmGray-600 text-xs uppercase tracking-wide">
                    Join Date
                  </th>
                </tr>
              </thead>

              {/* Compact Table Body */}
              <tbody className="divide-y divide-warmGray-100">
                {paginatedEmployees.map((employee, index) => (
                  <tr
                    key={employee.id}
                    className="hover:bg-warmGray-50/50 transition-colors duration-150"
                  >
                    {/* Member */}
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 rounded-full overflow-hidden bg-gradient-to-br from-[#E8C4A0] to-[#DDB892] flex items-center justify-center flex-shrink-0">
                          <UserIcon className="w-4 h-4 text-[#8B6F47]" />
                        </div>
                        <div className="min-w-0">
                          <div className="font-medium text-warmGray-900 text-sm truncate">
                            {employee.name || 'N/A'}
                          </div>
                          {Object.keys(employee.attributes_dict || {}).length > 0 && (
                            <div className="text-xs text-warmGray-500">
                              +{Object.keys(employee.attributes_dict).length} attributes
                            </div>
                          )}
                        </div>
                      </div>
                    </td>

                    {/* Email */}
                    <td className="py-3 px-4">
                      <span className="text-warmGray-700 text-sm">
                        {employee.email || 'N/A'}
                      </span>
                    </td>

                    {/* Campaigns */}
                    <td className="py-3 px-4">
                      <div className="flex flex-wrap gap-1">
                        {employee.campaigns.map((campaign, idx) => (
                          <span
                            key={`${campaign.id}-${idx}`}
                            className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                          >
                            {campaign.name}
                          </span>
                        ))}
                      </div>
                    </td>

                    {/* Join Date */}
                    <td className="py-3 px-4">
                      <span className="text-warmGray-600 text-sm">
                        {formatDate(employee.arrival_date)}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>


      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={totalItems}
            pageSize={pageSize}
            onPageChange={handlePageChange}
          />
        </div>
      )}
    </div>
  );
};

export default Employees;
