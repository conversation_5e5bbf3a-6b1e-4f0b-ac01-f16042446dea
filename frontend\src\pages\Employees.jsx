import React, { useState, useMemo } from 'react';
import {
  UserGroupIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  EyeIcon,
  CalendarDaysIcon,
  EnvelopeIcon,
  UserIcon,
  BuildingOfficeIcon
} from '@heroicons/react/24/outline';
import { useEmployees } from '../hooks/useEmployees';
import { useCampaigns } from '../hooks/useCampaigns';
import Pagination from '../components/ui/Pagination';
import EmployeeDetailsModal from '../components/employees/EmployeeDetailsModal';

const Employees = () => {
  // State for filters and pagination
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCampaign, setSelectedCampaign] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const pageSize = 6; // Selon vos préférences mémoire

  // Fetch campaigns for filter dropdown
  const { data: campaignsResponse } = useCampaigns({ page_size: 1000 });
  const campaigns = campaignsResponse?.data || [];

  // Prepare query parameters for employees
  const queryParams = useMemo(() => ({
    page: 1,
    page_size: 1000, // Récupérer tous pour pagination côté client
    search: '', // On filtre côté client
    ...(selectedCampaign && { campaign: selectedCampaign })
  }), [selectedCampaign]);

  const { data: employeesResponse, isLoading, error } = useEmployees(queryParams);
  const employees = employeesResponse?.results || employeesResponse || [];

  // Client-side filtering and pagination
  const filteredEmployees = useMemo(() => {
    if (!employees) return [];

    return employees.filter(employee => {
      const matchesSearch = !searchTerm ||
        employee.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        employee.email?.toLowerCase().includes(searchTerm.toLowerCase());

      return matchesSearch;
    });
  }, [employees, searchTerm]);

  // Pagination calculations
  const totalItems = filteredEmployees.length;
  const totalPages = Math.ceil(totalItems / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const paginatedEmployees = filteredEmployees.slice(startIndex, startIndex + pageSize);

  // Handlers
  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1); // Reset to first page
  };

  const handleCampaignFilter = (e) => {
    setSelectedCampaign(e.target.value);
    setCurrentPage(1);
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handleViewDetails = (employee) => {
    setSelectedEmployee(employee);
  };

  const handleCloseModal = () => {
    setSelectedEmployee(null);
  };

  const getSelectedEmployeeCampaign = () => {
    if (!selectedEmployee) return null;
    return campaigns.find(c => c.id === selectedEmployee.campaign);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const getCampaignName = (campaignId) => {
    const campaign = campaigns.find(c => c.id === campaignId);
    return campaign?.title || 'Unknown Campaign';
  };

  if (error) {
    return (
      <div className="max-w-7xl mx-auto">
        <div className="bg-red-50 border border-red-200 rounded-xl p-6 text-center">
          <div className="text-red-600 mb-2">⚠️ Error loading employees</div>
          <div className="text-red-500 text-sm">{error.message || 'Something went wrong'}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Simple Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-warmGray-800 mb-2">All Employees</h1>
        <p className="text-warmGray-600">
          View all employees across campaigns • {totalItems} total
        </p>
      </div>

      {/* Search & Filter */}
      <div className="bg-white rounded-2xl shadow-lg p-6">
        <div className="flex flex-col md:flex-row gap-4 items-center">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-warmGray-400" />
            <input
              type="text"
              placeholder="Search employees..."
              value={searchTerm}
              onChange={handleSearchChange}
              className="w-full pl-10 pr-4 py-3 border border-warmGray-300 rounded-xl focus:ring-2 focus:ring-[#E8C4A0] focus:border-transparent"
            />
          </div>

          {/* Campaign Filter */}
          <div className="relative">
            <FunnelIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-warmGray-400" />
            <select
              value={selectedCampaign}
              onChange={handleCampaignFilter}
              className="pl-10 pr-8 py-3 border border-warmGray-300 rounded-xl focus:ring-2 focus:ring-[#E8C4A0] focus:border-transparent appearance-none bg-white min-w-[200px]"
            >
              <option value="">All Campaigns</option>
              {campaigns.map(campaign => (
                <option key={campaign.id} value={campaign.id}>
                  {campaign.title}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Employees Table */}
      <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
        {isLoading ? (
          <div className="text-center py-16">
            <div className="animate-spin rounded-full h-12 w-12 border-2 border-[#E8C4A0] border-t-transparent mx-auto mb-4"></div>
            <div className="text-warmGray-600">Loading employees...</div>
          </div>
        ) : paginatedEmployees.length === 0 ? (
          <div className="text-center py-16">
            <UserGroupIcon className="h-16 w-16 text-warmGray-400 mx-auto mb-4" />
            <h3 className="text-xl font-medium text-warmGray-800 mb-2">
              {searchTerm || selectedCampaign ? 'No employees found' : 'No employees yet'}
            </h3>
            <p className="text-warmGray-600">
              {searchTerm || selectedCampaign
                ? 'Try adjusting your search or filter criteria'
                : 'Employees will appear here once they are imported into campaigns'
              }
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              {/* Table Header */}
              <thead>
                <tr className="border-b border-warmGray-200">
                  <th className="text-left py-4 px-6 font-medium text-warmGray-600 text-sm">
                    Member
                  </th>
                  <th className="text-left py-4 px-6 font-medium text-warmGray-600 text-sm">
                    Email
                  </th>
                  <th className="text-left py-4 px-6 font-medium text-warmGray-600 text-sm">
                    Join Date
                  </th>
                  <th className="text-left py-4 px-6 font-medium text-warmGray-600 text-sm">
                    Campaign
                  </th>
                  <th className="text-center py-4 px-6 font-medium text-warmGray-600 text-sm">

                  </th>
                </tr>
              </thead>

              {/* Table Body */}
              <tbody className="divide-y divide-warmGray-100">
                {paginatedEmployees.map((employee, index) => (
                  <tr
                    key={employee.id}
                    className="hover:bg-warmGray-50/50 transition-colors duration-150"
                  >
                    {/* Member */}
                    <td className="py-5 px-6">
                      <div className="flex items-center gap-4">
                        <div className="w-10 h-10 rounded-full overflow-hidden bg-gradient-to-br from-[#E8C4A0] to-[#DDB892] flex items-center justify-center flex-shrink-0">
                          <UserIcon className="w-5 h-5 text-[#8B6F47]" />
                        </div>
                        <div>
                          <div className="font-medium text-warmGray-900 text-base">
                            {employee.name || 'N/A'}
                          </div>
                        </div>
                      </div>
                    </td>

                    {/* Email */}
                    <td className="py-5 px-6">
                      <span className="text-warmGray-700">
                        {employee.email || 'N/A'}
                      </span>
                    </td>

                    {/* Join Date */}
                    <td className="py-5 px-6">
                      <span className="text-warmGray-600">
                        {formatDate(employee.arrival_date)}
                      </span>
                    </td>

                    {/* Campaign */}
                    <td className="py-5 px-6">
                      <div className="flex flex-wrap gap-2">
                        <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                          {getCampaignName(employee.campaign)}
                        </span>
                        {employee.attributes_dict && Object.keys(employee.attributes_dict).length > 0 && (
                          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                            +{Object.keys(employee.attributes_dict).length} attrs
                          </span>
                        )}
                      </div>
                    </td>

                    {/* Actions */}
                    <td className="py-5 px-6 text-center">
                      <button
                        onClick={() => handleViewDetails(employee)}
                        className="inline-flex items-center justify-center w-8 h-8 text-warmGray-400 hover:text-[#8B6F47] hover:bg-warmGray-100 rounded-lg transition-colors duration-150"
                        title="View Details"
                      >
                        <EyeIcon className="w-4 h-4" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>


      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={totalItems}
            pageSize={pageSize}
            onPageChange={handlePageChange}
          />
        </div>
      )}

      {/* Employee Details Modal */}
      {selectedEmployee && (
        <EmployeeDetailsModal
          employee={selectedEmployee}
          campaign={getSelectedEmployeeCampaign()}
          onClose={handleCloseModal}
        />
      )}
    </div>
  );
};

export default Employees;
