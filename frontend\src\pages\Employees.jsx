import React, { useState, useMemo } from 'react';
import {
  UserGroupIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  EyeIcon,
  DocumentArrowUpIcon,
  PlusIcon,
  CalendarDaysIcon,
  EnvelopeIcon,
  UserIcon
} from '@heroicons/react/24/outline';
import { useEmployees } from '../hooks/useEmployees';
import { useCampaigns } from '../hooks/useCampaigns';
import Pagination from '../components/ui/Pagination';
import EmployeeDetailsModal from '../components/employees/EmployeeDetailsModal';

const Employees = () => {
  // State for filters and pagination
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCampaign, setSelectedCampaign] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const pageSize = 6; // Selon vos préférences mémoire

  // Fetch campaigns for filter dropdown
  const { data: campaignsResponse } = useCampaigns({ page_size: 1000 });
  const campaigns = campaignsResponse?.data || [];

  // Prepare query parameters for employees
  const queryParams = useMemo(() => ({
    page: 1,
    page_size: 1000, // Récupérer tous pour pagination côté client
    search: '', // On filtre côté client
    ...(selectedCampaign && { campaign: selectedCampaign })
  }), [selectedCampaign]);

  const { data: employeesResponse, isLoading, error } = useEmployees(queryParams);
  const employees = employeesResponse?.results || employeesResponse || [];

  // Client-side filtering and pagination
  const filteredEmployees = useMemo(() => {
    if (!employees) return [];

    return employees.filter(employee => {
      const matchesSearch = !searchTerm ||
        employee.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        employee.email?.toLowerCase().includes(searchTerm.toLowerCase());

      return matchesSearch;
    });
  }, [employees, searchTerm]);

  // Pagination calculations
  const totalItems = filteredEmployees.length;
  const totalPages = Math.ceil(totalItems / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const paginatedEmployees = filteredEmployees.slice(startIndex, startIndex + pageSize);

  // Handlers
  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1); // Reset to first page
  };

  const handleCampaignFilter = (e) => {
    setSelectedCampaign(e.target.value);
    setCurrentPage(1);
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handleViewDetails = (employee) => {
    setSelectedEmployee(employee);
  };

  const handleCloseModal = () => {
    setSelectedEmployee(null);
  };

  const getSelectedEmployeeCampaign = () => {
    if (!selectedEmployee) return null;
    return campaigns.find(c => c.id === selectedEmployee.campaign);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const getCampaignName = (campaignId) => {
    const campaign = campaigns.find(c => c.id === campaignId);
    return campaign?.title || 'Unknown Campaign';
  };

  if (error) {
    return (
      <div className="max-w-7xl mx-auto">
        <div className="bg-red-50 border border-red-200 rounded-xl p-6 text-center">
          <div className="text-red-600 mb-2">⚠️ Error loading employees</div>
          <div className="text-red-500 text-sm">{error.message || 'Something went wrong'}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto space-y-4">
      {/* Compact Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-warmGray-800">Employees</h1>
          <p className="text-sm text-warmGray-600">
            {totalItems} employee{totalItems !== 1 ? 's' : ''} across all campaigns
          </p>
        </div>

        {/* Quick Actions */}
        <div className="flex items-center gap-2">
          <button className="flex items-center gap-2 px-3 py-2 bg-[#E8C4A0] hover:bg-[#DDB892] text-[#8B6F47] rounded-lg text-sm font-medium transition-colors">
            <DocumentArrowUpIcon className="w-4 h-4" />
            Import
          </button>
          <button className="flex items-center gap-2 px-3 py-2 bg-[#8B6F47] hover:bg-[#7A5F3F] text-white rounded-lg text-sm font-medium transition-colors">
            <PlusIcon className="w-4 h-4" />
            Add
          </button>
        </div>
      </div>

      {/* Compact Filters */}
      <div className="bg-white rounded-xl shadow-sm border border-warmGray-200 p-4">
        <div className="flex flex-col sm:flex-row gap-3">
          {/* Search */}
          <div className="relative flex-1">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-warmGray-400" />
            <input
              type="text"
              placeholder="Search by name or email..."
              value={searchTerm}
              onChange={handleSearchChange}
              className="w-full pl-9 pr-4 py-2 border border-warmGray-300 rounded-lg text-sm focus:ring-2 focus:ring-[#E8C4A0] focus:border-transparent"
            />
          </div>

          {/* Campaign Filter */}
          <div className="relative sm:w-48">
            <FunnelIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-warmGray-400" />
            <select
              value={selectedCampaign}
              onChange={handleCampaignFilter}
              className="w-full pl-9 pr-8 py-2 border border-warmGray-300 rounded-lg text-sm focus:ring-2 focus:ring-[#E8C4A0] focus:border-transparent appearance-none bg-white"
            >
              <option value="">All Campaigns</option>
              {campaigns.map(campaign => (
                <option key={campaign.id} value={campaign.id}>
                  {campaign.title}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Employees Table */}
      <div className="bg-white rounded-xl shadow-sm border border-warmGray-200 overflow-hidden">
        {isLoading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-2 border-[#E8C4A0] border-t-transparent mx-auto mb-4"></div>
            <div className="text-warmGray-600">Loading employees...</div>
          </div>
        ) : paginatedEmployees.length === 0 ? (
          <div className="p-8 text-center">
            <UserGroupIcon className="h-12 w-12 text-warmGray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-warmGray-800 mb-2">
              {searchTerm || selectedCampaign ? 'No employees found' : 'No employees yet'}
            </h3>
            <p className="text-warmGray-600 mb-4">
              {searchTerm || selectedCampaign
                ? 'Try adjusting your search or filter criteria'
                : 'Import employees from Excel or add them manually to get started'
              }
            </p>
            {!searchTerm && !selectedCampaign && (
              <button className="flex items-center gap-2 px-4 py-2 bg-[#E8C4A0] hover:bg-[#DDB892] text-[#8B6F47] rounded-lg font-medium transition-colors mx-auto">
                <DocumentArrowUpIcon className="w-4 h-4" />
                Import Employees
              </button>
            )}
          </div>
        ) : (
          <>
            {/* Table Header */}
            <div className="bg-warmGray-50 border-b border-warmGray-200 px-6 py-3">
              <div className="grid grid-cols-12 gap-4 text-xs font-medium text-warmGray-700 uppercase tracking-wider">
                <div className="col-span-3">Name</div>
                <div className="col-span-3">Email</div>
                <div className="col-span-2">Join Date</div>
                <div className="col-span-3">Campaign</div>
                <div className="col-span-1 text-center">Actions</div>
              </div>
            </div>

            {/* Table Body */}
            <div className="divide-y divide-warmGray-200">
              {paginatedEmployees.map((employee, index) => (
                <div
                  key={employee.id}
                  className="px-6 py-4 hover:bg-warmGray-50 transition-colors"
                >
                  <div className="grid grid-cols-12 gap-4 items-center">
                    {/* Name */}
                    <div className="col-span-3">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-[#E8C4A0] rounded-full flex items-center justify-center flex-shrink-0">
                          <UserIcon className="w-4 h-4 text-[#8B6F47]" />
                        </div>
                        <div className="min-w-0">
                          <div className="text-sm font-medium text-warmGray-900 truncate">
                            {employee.name || 'N/A'}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Email */}
                    <div className="col-span-3">
                      <div className="flex items-center gap-2">
                        <EnvelopeIcon className="w-4 h-4 text-warmGray-400 flex-shrink-0" />
                        <span className="text-sm text-warmGray-900 truncate">
                          {employee.email || 'N/A'}
                        </span>
                      </div>
                    </div>

                    {/* Join Date */}
                    <div className="col-span-2">
                      <div className="flex items-center gap-2">
                        <CalendarDaysIcon className="w-4 h-4 text-warmGray-400 flex-shrink-0" />
                        <span className="text-sm text-warmGray-600">
                          {formatDate(employee.arrival_date)}
                        </span>
                      </div>
                    </div>

                    {/* Campaign */}
                    <div className="col-span-3">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-[#E8C4A0]/20 text-[#8B6F47] truncate">
                        {getCampaignName(employee.campaign)}
                      </span>
                    </div>

                    {/* Actions */}
                    <div className="col-span-1 text-center">
                      <button
                        onClick={() => handleViewDetails(employee)}
                        className="p-1.5 text-warmGray-400 hover:text-[#8B6F47] hover:bg-[#E8C4A0]/20 rounded-lg transition-colors"
                        title="View Details"
                      >
                        <EyeIcon className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </>
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={totalItems}
            pageSize={pageSize}
            onPageChange={handlePageChange}
          />
        </div>
      )}

      {/* Employee Details Modal */}
      {selectedEmployee && (
        <EmployeeDetailsModal
          employee={selectedEmployee}
          campaign={getSelectedEmployeeCampaign()}
          onClose={handleCloseModal}
        />
      )}
    </div>
  );
};

export default Employees;
