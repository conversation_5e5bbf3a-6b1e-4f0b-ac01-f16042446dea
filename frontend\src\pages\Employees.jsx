import React, { useState, useMemo } from 'react';
import {
  UserGroupIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  EyeIcon,
  CalendarDaysIcon,
  EnvelopeIcon,
  UserIcon,
  BuildingOfficeIcon
} from '@heroicons/react/24/outline';
import { useEmployees } from '../hooks/useEmployees';
import { useCampaigns } from '../hooks/useCampaigns';
import Pagination from '../components/ui/Pagination';
import EmployeeDetailsModal from '../components/employees/EmployeeDetailsModal';

const Employees = () => {
  // State for filters and pagination
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCampaign, setSelectedCampaign] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const pageSize = 6; // Selon vos préférences mémoire

  // Fetch campaigns for filter dropdown
  const { data: campaignsResponse } = useCampaigns({ page_size: 1000 });
  const campaigns = campaignsResponse?.data || [];

  // Prepare query parameters for employees
  const queryParams = useMemo(() => ({
    page: 1,
    page_size: 1000, // Récupérer tous pour pagination côté client
    search: '', // On filtre côté client
    ...(selectedCampaign && { campaign: selectedCampaign })
  }), [selectedCampaign]);

  const { data: employeesResponse, isLoading, error } = useEmployees(queryParams);
  const employees = employeesResponse?.results || employeesResponse || [];

  // Client-side filtering and pagination
  const filteredEmployees = useMemo(() => {
    if (!employees) return [];

    return employees.filter(employee => {
      const matchesSearch = !searchTerm ||
        employee.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        employee.email?.toLowerCase().includes(searchTerm.toLowerCase());

      return matchesSearch;
    });
  }, [employees, searchTerm]);

  // Pagination calculations
  const totalItems = filteredEmployees.length;
  const totalPages = Math.ceil(totalItems / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const paginatedEmployees = filteredEmployees.slice(startIndex, startIndex + pageSize);

  // Handlers
  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1); // Reset to first page
  };

  const handleCampaignFilter = (e) => {
    setSelectedCampaign(e.target.value);
    setCurrentPage(1);
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handleViewDetails = (employee) => {
    setSelectedEmployee(employee);
  };

  const handleCloseModal = () => {
    setSelectedEmployee(null);
  };

  const getSelectedEmployeeCampaign = () => {
    if (!selectedEmployee) return null;
    return campaigns.find(c => c.id === selectedEmployee.campaign);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const getCampaignName = (campaignId) => {
    const campaign = campaigns.find(c => c.id === campaignId);
    return campaign?.title || 'Unknown Campaign';
  };

  if (error) {
    return (
      <div className="max-w-7xl mx-auto">
        <div className="bg-red-50 border border-red-200 rounded-xl p-6 text-center">
          <div className="text-red-600 mb-2">⚠️ Error loading employees</div>
          <div className="text-red-500 text-sm">{error.message || 'Something went wrong'}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Simple Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-warmGray-800 mb-2">All Employees</h1>
        <p className="text-warmGray-600">
          View all employees across campaigns • {totalItems} total
        </p>
      </div>

      {/* Search & Filter */}
      <div className="bg-white rounded-2xl shadow-lg p-6">
        <div className="flex flex-col md:flex-row gap-4 items-center">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-warmGray-400" />
            <input
              type="text"
              placeholder="Search employees..."
              value={searchTerm}
              onChange={handleSearchChange}
              className="w-full pl-10 pr-4 py-3 border border-warmGray-300 rounded-xl focus:ring-2 focus:ring-[#E8C4A0] focus:border-transparent"
            />
          </div>

          {/* Campaign Filter */}
          <div className="relative">
            <FunnelIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-warmGray-400" />
            <select
              value={selectedCampaign}
              onChange={handleCampaignFilter}
              className="pl-10 pr-8 py-3 border border-warmGray-300 rounded-xl focus:ring-2 focus:ring-[#E8C4A0] focus:border-transparent appearance-none bg-white min-w-[200px]"
            >
              <option value="">All Campaigns</option>
              {campaigns.map(campaign => (
                <option key={campaign.id} value={campaign.id}>
                  {campaign.title}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Employees Cards */}
      {isLoading ? (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-2 border-[#E8C4A0] border-t-transparent mx-auto mb-4"></div>
          <div className="text-warmGray-600">Loading employees...</div>
        </div>
      ) : paginatedEmployees.length === 0 ? (
        <div className="text-center py-12">
          <UserGroupIcon className="h-16 w-16 text-warmGray-400 mx-auto mb-4" />
          <h3 className="text-xl font-medium text-warmGray-800 mb-2">
            {searchTerm || selectedCampaign ? 'No employees found' : 'No employees yet'}
          </h3>
          <p className="text-warmGray-600">
            {searchTerm || selectedCampaign
              ? 'Try adjusting your search or filter criteria'
              : 'Employees will appear here once they are imported into campaigns'
            }
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {paginatedEmployees.map((employee) => (
            <div
              key={employee.id}
              className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 border border-warmGray-100"
            >
              {/* Employee Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-gradient-to-br from-[#E8C4A0] to-[#DDB892] rounded-full flex items-center justify-center">
                    <UserIcon className="w-6 h-6 text-[#8B6F47]" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-warmGray-800 text-lg">
                      {employee.name || 'N/A'}
                    </h3>
                    <p className="text-sm text-warmGray-500">Employee</p>
                  </div>
                </div>
                <button
                  onClick={() => handleViewDetails(employee)}
                  className="p-2 text-warmGray-400 hover:text-[#8B6F47] hover:bg-[#E8C4A0]/20 rounded-lg transition-colors"
                  title="View Details"
                >
                  <EyeIcon className="w-5 h-5" />
                </button>
              </div>

              {/* Employee Info */}
              <div className="space-y-3">
                <div className="flex items-center gap-2 text-sm">
                  <EnvelopeIcon className="w-4 h-4 text-warmGray-400" />
                  <span className="text-warmGray-700 truncate">{employee.email || 'N/A'}</span>
                </div>

                <div className="flex items-center gap-2 text-sm">
                  <CalendarDaysIcon className="w-4 h-4 text-warmGray-400" />
                  <span className="text-warmGray-700">Joined {formatDate(employee.arrival_date)}</span>
                </div>

                <div className="flex items-center gap-2 text-sm">
                  <BuildingOfficeIcon className="w-4 h-4 text-warmGray-400" />
                  <span className="text-[#8B6F47] font-medium truncate">
                    {getCampaignName(employee.campaign)}
                  </span>
                </div>
              </div>

              {/* Additional Attributes Preview */}
              {employee.attributes_dict && Object.keys(employee.attributes_dict).length > 0 && (
                <div className="mt-4 pt-4 border-t border-warmGray-100">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-2 h-2 bg-[#E8C4A0] rounded-full"></div>
                    <span className="text-xs font-medium text-warmGray-600 uppercase tracking-wide">
                      Additional Info
                    </span>
                  </div>
                  <div className="text-xs text-warmGray-500">
                    {Object.keys(employee.attributes_dict).length} attribute{Object.keys(employee.attributes_dict).length !== 1 ? 's' : ''} available
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}


      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={totalItems}
            pageSize={pageSize}
            onPageChange={handlePageChange}
          />
        </div>
      )}

      {/* Employee Details Modal */}
      {selectedEmployee && (
        <EmployeeDetailsModal
          employee={selectedEmployee}
          campaign={getSelectedEmployeeCampaign()}
          onClose={handleCloseModal}
        />
      )}
    </div>
  );
};

export default Employees;
